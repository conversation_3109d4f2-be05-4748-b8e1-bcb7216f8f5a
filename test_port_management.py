#!/usr/bin/env python3
"""
Test Port Management Functionality
Tests the robust port finding and management features
"""

import sys
import os
import socket
import time

# Add paths
sys.path.append('4_helper_tools')

def test_port_availability():
    """Test basic port availability checking"""
    print("Testing Port Availability...")
    print("=" * 40)
    
    def is_port_available(port):
        """Check if a port is available"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                sock.bind(('', port))
                return True
        except socket.error:
            return False
    
    # Test common ports
    test_ports = [8080, 8081, 8082, 8000, 8001]
    
    for port in test_ports:
        available = is_port_available(port)
        status = "✓ Available" if available else "✗ In Use"
        print(f"Port {port}: {status}")
    
    return test_ports

def test_port_finding():
    """Test automatic port finding"""
    print("\nTesting Automatic Port Finding...")
    print("=" * 40)
    
    def find_available_port(start_port=8000, max_port=8100):
        """Find an available port in the given range"""
        for port in range(start_port, max_port):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as sock:
                    sock.bind(('', port))
                    return port
            except socket.error:
                continue
        return None
    
    # Find available ports
    available_port = find_available_port(8080, 8090)
    
    if available_port:
        print(f"✓ Found available port: {available_port}")
        return available_port
    else:
        print("✗ No available ports found in range 8080-8090")
        return None

def test_robust_server_manager():
    """Test the robust server manager if available"""
    print("\nTesting Robust Server Manager...")
    print("=" * 40)
    
    try:
        from atlas_robust_server_manager import RobustServerManager
        
        # Create a test manager
        manager = RobustServerManager(None, port=8080)
        print("✓ RobustServerManager imported successfully")
        
        # Test port availability checking
        if manager.is_port_available(8080):
            print("✓ Port 8080 is available")
        else:
            print("✗ Port 8080 is not available")
        
        # Test port finding
        try:
            available_port = manager.find_available_port(8080, 8090)
            print(f"✓ Found available port: {available_port}")
        except Exception as e:
            print(f"✗ Port finding failed: {e}")
        
        return True
        
    except ImportError as e:
        print(f"✗ Could not import RobustServerManager: {e}")
        return False
    except Exception as e:
        print(f"✗ Error testing RobustServerManager: {e}")
        return False

def test_system_requirements():
    """Test system requirements checking"""
    print("\nTesting System Requirements...")
    print("=" * 40)
    
    try:
        from atlas_robust_server_manager import check_system_requirements
        
        if check_system_requirements():
            print("✓ System requirements check passed")
            return True
        else:
            print("✗ System requirements check failed")
            return False
            
    except ImportError:
        print("✗ Could not import check_system_requirements")
        return False
    except Exception as e:
        print(f"✗ Error checking system requirements: {e}")
        return False

def main():
    """Main test function"""
    print("A.T.L.A.S. Port Management Test")
    print("=" * 50)
    
    # Test basic port functionality
    test_ports = test_port_availability()
    
    # Test port finding
    available_port = test_port_finding()
    
    # Test robust server manager
    manager_works = test_robust_server_manager()
    
    # Test system requirements
    requirements_ok = test_system_requirements()
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Port Availability Test: ✓ Completed")
    print(f"Port Finding Test: {'✓ Passed' if available_port else '✗ Failed'}")
    print(f"Robust Server Manager: {'✓ Working' if manager_works else '✗ Not Available'}")
    print(f"System Requirements: {'✓ Passed' if requirements_ok else '✗ Failed'}")
    
    if manager_works and available_port:
        print("\n✓ Port management functionality is working correctly!")
        print(f"✓ Server can start on port {available_port} if 8080 is busy")
    else:
        print("\n⚠️ Some port management features may not be available")
    
    print("\nTo test with actual server startup:")
    print("1. Run: python enhanced_atlas_startup.py")
    print("2. If port 8080 is busy, it should automatically find an alternative")
    print("3. Check the console output for port switching messages")

if __name__ == "__main__":
    main()
