#!/usr/bin/env python3
"""
Simple Unicode Test - Verify emoji replacement works
"""

import logging
import sys
import os

# Set environment for Windows
os.environ['PYTHONIOENCODING'] = 'utf-8'

class SimpleUnicodeFormatter(logging.Formatter):
    """Simple formatter that replaces emojis with ASCII"""
    
    def format(self, record):
        formatted = super().format(record)
        
        # Replace common emojis
        replacements = {
            '🔮': '[AI]',
            '✅': '[OK]',
            '❌': '[ERROR]',
            '📊': '[DATA]',
            '🚀': '[LAUNCH]',
            '📚': '[LIBRARY]',
            '📖': '[BOOK]',
            '🧹': '[CLEAN]'
        }
        
        for emoji, replacement in replacements.items():
            formatted = formatted.replace(emoji, replacement)
        
        # Ensure ASCII
        try:
            formatted = formatted.encode('ascii', 'replace').decode('ascii')
        except:
            formatted = ''.join(c for c in formatted if ord(c) < 128)
        
        return formatted

def test_unicode_fix():
    """Test the Unicode fix"""
    
    print("Simple Unicode Test")
    print("=" * 30)
    
    # Setup logging
    logger = logging.getLogger('test')
    handler = logging.StreamHandler(sys.stdout)
    formatter = SimpleUnicodeFormatter('%(asctime)s [%(levelname)s] %(name)s: %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)
    
    # Test emoji logging
    print("Testing emoji replacement...")
    logger.info("Risk Engine: ✅ Risk Engine initialization completed")
    logger.info("Education Engine: 📚 Education Engine created")
    logger.info("Market Engine: ✅ Market Engine initialization completed")
    logger.info("AI Engine: 🔮 Predicto AI Engine cleanup completed")
    logger.info("Cleanup: 🧹 Cleaning up components")
    
    print("✓ Unicode test completed successfully!")
    print("If you see [OK], [LIBRARY], [AI], [CLEAN] instead of emojis, the fix is working!")

if __name__ == "__main__":
    test_unicode_fix()
