#!/usr/bin/env python3
"""
Test script to verify A.T.L.A.S. API responses
"""

import requests
import json
import sys

def test_atlas_api():
    """Test the A.T.L.A.S. chat API"""
    
    url = "http://localhost:8080/api/v1/chat"
    
    test_messages = [
        "hello",
        "what can you do?",
        "analyze NVDA stock",
        "what are your capabilities?"
    ]
    
    print("🧪 Testing A.T.L.A.S. API Responses")
    print("=" * 50)
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n📤 Test {i}: '{message}'")
        print("-" * 30)
        
        payload = {
            "message": message,
            "session_id": f"test_session_{i}"
        }
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"Response Type: {type(response_data)}")
                    
                    if isinstance(response_data, dict):
                        if 'response' in response_data:
                            print(f"✅ Response: {response_data['response']}")
                        else:
                            print(f"📋 Full Response: {json.dumps(response_data, indent=2)}")
                    else:
                        print(f"📋 Raw Response: {response_data}")
                        
                except json.JSONDecodeError:
                    print(f"📄 Text Response: {response.text}")
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Error Text: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Request failed: {e}")
        
        print()

if __name__ == "__main__":
    test_atlas_api()
