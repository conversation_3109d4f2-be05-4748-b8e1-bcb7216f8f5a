#!/usr/bin/env python3
"""
Minimal A.T.L.A.S. Startup Script with Unicode Fix
Focuses on resolving the Windows Unicode encoding issues
"""

import sys
import os

# Set environment variables for Windows Unicode support
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Add paths
sys.path.append('4_helper_tools')
sys.path.append('1_main_chat_engine')
sys.path.append('2_trading_logic')
sys.path.append('3_market_news_data')

print("Starting A.T.L.A.S. with Unicode fix...")
print("=" * 50)

try:
    # Setup Windows-compatible logging first
    from atlas_logging_config import setup_atlas_logging, get_atlas_logger
    setup_atlas_logging('INFO')
    logger = get_atlas_logger('minimal_startup')
    
    logger.info("A.T.L.A.S. minimal startup initiated")
    logger.info("Unicode encoding issues should be resolved")
    
    # Import and start the server
    import uvicorn
    from atlas_server import app
    
    logger.info("Starting server on port 8080")
    print("Server will be available at: http://localhost:8080")
    print("API docs: http://localhost:8080/docs")
    print("Health check: http://localhost:8080/api/v1/health")
    
    # Start with basic uvicorn but with logging fixes
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8080,
        reload=False,
        log_level="info"
    )
    
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all dependencies are installed")
    sys.exit(1)
    
except Exception as e:
    print(f"Startup error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
