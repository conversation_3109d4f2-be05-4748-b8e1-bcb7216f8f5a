"""
Enhanced Server Startup with Port Management for A.T.L.A.S
Robust server startup solution with Windows compatibility and port management
"""

import os
import sys
import socket
import time
import psutil
import uvicorn
from fastapi import FastAPI
from contextlib import closing
import logging
import signal
import atexit
from typing import Optional
from atlas_logging_config import setup_atlas_logging, get_atlas_logger

# Configure logging with Windows compatibility
setup_atlas_logging()
logger = get_atlas_logger(__name__)

class RobustServerManager:
    """Handles robust server startup with Windows compatibility"""
    
    def __init__(self, app: FastAPI, host: str = "0.0.0.0", port: int = 8000):
        self.app = app
        self.host = host
        self.port = port
        self.server = None
        self.original_port = port
        
    def find_and_kill_process_on_port(self, port: int) -> bool:
        """Find and kill any process using the specified port"""
        try:
            for conn in psutil.net_connections():
                if conn.laddr.port == port and conn.status == 'LISTEN':
                    try:
                        process = psutil.Process(conn.pid)
                        logger.warning(f"Found process {process.name()} (PID: {conn.pid}) using port {port}")
                        process.terminate()
                        process.wait(timeout=5)
                        logger.info(f"Successfully terminated process on port {port}")
                        return True
                    except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                        logger.error(f"Could not terminate process: {e}")
                        return False
        except Exception as e:
            logger.error(f"Error checking port {port}: {e}")
            return False
        
        return False
    
    def is_port_available(self, port: int, max_retries: int = 3) -> bool:
        """Check if port is available with retries"""
        for attempt in range(max_retries):
            with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
                try:
                    sock.bind(('', port))
                    return True
                except socket.error:
                    if attempt < max_retries - 1:
                        logger.warning(f"Port {port} not available, attempt {attempt + 1}/{max_retries}")
                        time.sleep(2)
                    else:
                        return False
        return False
    
    def find_available_port(self, start_port: int = 8000, max_port: int = 8100) -> int:
        """Find an available port in the given range"""
        for port in range(start_port, max_port):
            if self.is_port_available(port, max_retries=1):
                return port
        raise RuntimeError(f"No available ports found in range {start_port}-{max_port}")
    
    def cleanup_on_exit(self):
        """Cleanup function to ensure proper shutdown"""
        logger.info("Performing cleanup on exit...")
        if self.server:
            self.server.should_exit = True
    
    def start_server(self, auto_find_port: bool = True):
        """Start the FastAPI server with robust error handling"""
        # Register cleanup handlers
        atexit.register(self.cleanup_on_exit)
        
        # Only register signal handlers on Unix-like systems
        if hasattr(signal, 'SIGINT'):
            signal.signal(signal.SIGINT, lambda s, f: self.cleanup_on_exit())
        if hasattr(signal, 'SIGTERM'):
            signal.signal(signal.SIGTERM, lambda s, f: self.cleanup_on_exit())
        
        try:
            # Check if port is available
            if not self.is_port_available(self.port):
                logger.warning(f"Port {self.port} is not available")
                
                # Try to kill the process using the port
                if self.find_and_kill_process_on_port(self.port):
                    time.sleep(2)  # Give OS time to release the port
                    
                    # Check again
                    if not self.is_port_available(self.port):
                        if auto_find_port:
                            self.port = self.find_available_port(self.original_port)
                            logger.info(f"Using alternative port: {self.port}")
                        else:
                            raise RuntimeError(f"Port {self.port} is still not available")
                elif auto_find_port:
                    self.port = self.find_available_port(self.original_port)
                    logger.info(f"Using alternative port: {self.port}")
                else:
                    raise RuntimeError(f"Cannot bind to port {self.port}")
            
            # Configure uvicorn with Windows-specific settings
            config = uvicorn.Config(
                app=self.app,
                host=self.host,
                port=self.port,
                reload=False,  # Disable reload on Windows to prevent issues
                workers=1,     # Single worker for Windows compatibility
                loop="asyncio",
                log_level="info",
                access_log=True
            )
            
            self.server = uvicorn.Server(config)
            
            logger.info(f"Starting A.T.L.A.S. server on {self.host}:{self.port}")
            logger.info(f"Access the web interface at http://localhost:{self.port}")
            logger.info(f"API documentation available at http://localhost:{self.port}/docs")
            
            # Start server
            self.server.run()
            
        except Exception as e:
            logger.error(f"Failed to start server: {e}")
            raise
        finally:
            self.cleanup_on_exit()


def start_atlas_server(app: FastAPI, port: int = 8000, host: str = "0.0.0.0") -> None:
    """Enhanced startup function with Windows compatibility"""
    
    # Ensure logs directory exists
    os.makedirs('logs', exist_ok=True)
    
    # Create server manager
    server_manager = RobustServerManager(app, host=host, port=port)
    
    # Add startup event to app
    @app.on_event("startup")
    async def startup_event():
        logger.info("A.T.L.A.S. server starting up...")
        logger.info(f"Python version: {sys.version}")
        logger.info(f"Platform: {sys.platform}")
        logger.info(f"Process ID: {os.getpid()}")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        logger.info("A.T.L.A.S. server shutting down...")
    
    try:
        # Start server with auto port finding enabled
        server_manager.start_server(auto_find_port=True)
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server error: {e}")
        sys.exit(1)


def check_system_requirements() -> bool:
    """Check if system meets requirements for A.T.L.A.S."""
    logger.info("Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("Python 3.8 or higher is required")
        return False
    
    # Check available memory
    try:
        memory = psutil.virtual_memory()
        if memory.available < 1024 * 1024 * 1024:  # 1GB
            logger.warning("Less than 1GB of available memory detected")
    except Exception as e:
        logger.warning(f"Could not check memory: {e}")
    
    # Check disk space
    try:
        disk = psutil.disk_usage('.')
        if disk.free < 1024 * 1024 * 1024:  # 1GB
            logger.warning("Less than 1GB of free disk space")
    except Exception as e:
        logger.warning(f"Could not check disk space: {e}")
    
    logger.info("System requirements check completed")
    return True


if __name__ == "__main__":
    # Test the robust server manager
    from fastapi import FastAPI
    
    test_app = FastAPI(title="A.T.L.A.S. Test Server")
    
    @test_app.get("/")
    async def root():
        return {"message": "A.T.L.A.S. Test Server is running"}
    
    @test_app.get("/health")
    async def health():
        return {"status": "healthy", "message": "Server is operational"}
    
    # Check system requirements
    if check_system_requirements():
        # Start the test server
        start_atlas_server(test_app, port=8080)
    else:
        logger.error("System requirements not met")
        sys.exit(1)
