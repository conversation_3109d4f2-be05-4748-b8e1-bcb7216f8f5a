<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>A.T.L.A.S. - Advanced Trading & Learning Analytics System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Segoe UI', system-ui, -apple-system, sans-serif;
            background: #0a0e1a;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* Background pattern */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(0, 255, 255, 0.05) 0%, transparent 50%),
                linear-gradient(135deg, #0a0e1a 0%, #0f1419 50%, #0a0e1a 100%);
            z-index: -1;
        }

        .main-container {
            display: flex;
            height: 100vh;
            padding: 20px;
            gap: 20px;
            max-width: 1400px;
            margin: 0 auto;
        }

        /* Left Panel - Chat Interface */
        .left-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: rgba(15, 20, 25, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 255, 255, 0.2);
            overflow: hidden;
            position: relative;
            min-height: 0; /* Important for flex child to shrink */
            height: 100%; /* Ensure full height */
        }

        .left-panel::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        /* Header with A.T.L.A.S. branding */
        .header {
            text-align: center;
            padding: 30px 20px 20px;
            position: relative;
            z-index: 1;
        }

        .atlas-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
            margin-bottom: 10px;
        }

        .chip-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #00ffff, #0080ff);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
        }

        .chip-icon::before {
            content: 'AI';
            color: #000;
            font-weight: 900;
            font-size: 18px;
            font-family: 'Inter', sans-serif;
        }

        /* Chip pins */
        .chip-icon::after {
            content: '';
            position: absolute;
            top: -5px;
            left: 10px;
            right: 10px;
            height: 2px;
            background: #00ffff;
            box-shadow:
                0 -3px 0 #00ffff,
                0 3px 0 #00ffff,
                0 6px 0 #00ffff;
        }

        .atlas-title {
            font-size: 2.8em;
            font-weight: 300;
            letter-spacing: 0.1em;
            background: linear-gradient(135deg, #00ffff, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        /* Chat Messages Area */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 0 20px;
            position: relative;
            z-index: 1;
            min-height: 0; /* Important for flex child to shrink */
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 20px 0;
            scroll-behavior: smooth;
            min-height: 0; /* Important for flex child to shrink */
            max-height: calc(100vh - 300px); /* Ensure space for input */
            display: flex;
            flex-direction: column;
        }

        .message {
            margin-bottom: 20px;
            padding: 20px;
            border-radius: 16px;
            max-width: 85%;
            word-wrap: break-word;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .message.user {
            background: rgba(0, 128, 255, 0.15);
            border: 1px solid rgba(0, 128, 255, 0.3);
            margin-left: auto;
            text-align: left;
            border-radius: 16px 16px 4px 16px;
        }

        .message.assistant {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.2);
            margin-right: auto;
            border-radius: 16px 16px 16px 4px;
        }

        .message-content {
            line-height: 1.6;
            font-size: 15px;
        }

        /* Chat Input */
        .chat-input-container {
            padding: 20px;
            position: relative;
            z-index: 10; /* Higher z-index to stay on top */
            background: rgba(15, 20, 25, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 255, 255, 0.1);
            flex-shrink: 0; /* Prevent shrinking */
        }

        .chat-input-form {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
            padding: 16px 20px;
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 25px;
            color: #ffffff;
            font-size: 15px;
            outline: none;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .chat-input:focus {
            border-color: #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.2);
            background: rgba(0, 0, 0, 0.6);
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .input-actions {
            display: flex;
            gap: 8px;
        }

        .action-btn {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            border: 1px solid rgba(0, 255, 255, 0.3);
            background: rgba(0, 255, 255, 0.1);
            color: #00ffff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .action-btn:hover {
            background: rgba(0, 255, 255, 0.2);
            border-color: #00ffff;
            transform: scale(1.05);
        }

        .send-button {
            background: linear-gradient(135deg, #00ffff, #0080ff);
            border: none;
            color: #000;
            font-weight: 600;
        }

        .send-button:hover {
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.4);
        }

        /* Right Panel - Trading Data */
        .right-panel {
            width: 400px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .trading-card {
            background: rgba(15, 20, 25, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(0, 255, 255, 0.2);
            padding: 20px;
            position: relative;
            overflow: hidden;
        }

        .trading-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(0, 255, 255, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
        }

        .ttm-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #00ffff;
        }

        .star-rating {
            display: flex;
            gap: 2px;
        }

        .star {
            color: #00ffff;
            font-size: 16px;
        }

        .chart-container {
            height: 200px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            margin: 15px 0;
            position: relative;
            overflow: hidden;
        }

        .candlestick-chart {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg,
                transparent 0%,
                rgba(0, 255, 255, 0.1) 25%,
                transparent 50%,
                rgba(255, 0, 100, 0.1) 75%,
                transparent 100%);
            position: relative;
        }

        /* Simulated candlestick bars */
        .candlestick-chart::after {
            content: '';
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 60px;
            background:
                linear-gradient(90deg,
                    #ff4444 0%, #ff4444 8%, transparent 8%, transparent 16%,
                    #00ff88 16%, #00ff88 24%, transparent 24%, transparent 32%,
                    #ff4444 32%, #ff4444 40%, transparent 40%, transparent 48%,
                    #00ff88 48%, #00ff88 56%, transparent 56%, transparent 64%,
                    #00ff88 64%, #00ff88 72%, transparent 72%, transparent 80%,
                    #00ff88 80%, #00ff88 88%, transparent 88%, transparent 96%,
                    #00ff88 96%, #00ff88 100%);
            opacity: 0.8;
        }

        .squeeze-indicator {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.3);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
            text-align: center;
            color: #00ffff;
        }

        .trade-suggestions {
            margin-top: 15px;
            position: relative;
            z-index: 1;
        }

        .trade-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 0;
            border-bottom: 1px solid rgba(0, 255, 255, 0.1);
            font-size: 14px;
        }

        .trade-item:last-child {
            border-bottom: none;
        }

        .trade-icon {
            width: 20px;
            text-align: center;
            color: #00ffff;
        }

        .safe-option {
            background: rgba(0, 255, 255, 0.1);
            border: 1px solid rgba(0, 255, 255, 0.2);
            border-radius: 12px;
            padding: 15px;
            margin-top: 15px;
            position: relative;
            z-index: 1;
        }

        .safe-option-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-weight: 600;
            color: #00ffff;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <!-- Left Panel - Chat Interface -->
        <div class="left-panel">
            <div class="header">
                <div class="atlas-logo">
                    <div class="chip-icon"></div>
                    <div class="atlas-title">A.T.L.A.S.</div>
                </div>
            </div>

            <div class="chat-container">
                <div class="chat-messages" id="chatMessages">
                    <div class="message assistant">
                        <div class="message-content">
                            Hello! I'm A.T.L.A.S. (Advanced Trading & Learning Analysis System) powered by Predicto. I'm here to help you with stock analysis, market insights, trading strategies, and educational content. What would you like to explore today?
                        </div>
                    </div>
                </div>

                <div class="chat-input-container">
                    <form class="chat-input-form" id="chatForm">
                        <input
                            type="text"
                            class="chat-input"
                            id="chatInput"
                            placeholder="Message A.T.L.A.S..."
                            autocomplete="off"
                        >
                        <div class="input-actions">
                            <button type="submit" class="action-btn send-button" id="sendButton">➤</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Right Panel - Trading Data -->
        <div class="right-panel">
            <div class="trading-card">
                <div class="card-header">
                    <div class="ttm-indicator">
                        <span>TTM</span>
                        <div class="star-rating">
                            <span class="star">⭐</span>
                            <span class="star">⭐</span>
                            <span class="star">⭐</span>
                        </div>
                    </div>
                </div>

                <div class="chart-container">
                    <div class="candlestick-chart"></div>
                </div>

                <div class="squeeze-indicator">
                    Squeeze signal
                </div>
            </div>

            <div class="trading-card">
                <div class="safe-option">
                    <div class="safe-option-header">
                        <span>[OK]</span>
                        <span>A.T.L.A.S. is ready</span>
                    </div>
                    <div>System initialized with Unicode-safe logging. All trading features are available.</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        class AtlasInterface {
            constructor() {
                this.chatMessages = document.getElementById('chatMessages');
                this.chatInput = document.getElementById('chatInput');
                this.sendButton = document.getElementById('sendButton');
                this.chatForm = document.getElementById('chatForm');
                this.sessionId = this.generateSessionId();
                this.isTyping = false;
                this.init();
            }

            init() {
                this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
                this.chatInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.handleSubmit(e);
                    }
                });
                this.chatInput.focus();
            }

            generateSessionId() {
                return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            }

            async handleSubmit(e) {
                e.preventDefault();
                const message = this.chatInput.value.trim();
                if (!message || this.isTyping) return;

                this.addMessage(message, 'user');
                this.chatInput.value = '';
                this.setTyping(true);

                try {
                    const response = await this.sendMessage(message);
                    this.addMessage(response.response, 'assistant', response);
                } catch (error) {
                    this.addMessage('Sorry, I encountered an error. Please try again.', 'assistant', { type: 'error' });
                    console.error('Chat error:', error);
                } finally {
                    this.setTyping(false);
                }
            }

            async sendMessage(message) {
                try {
                    const response = await fetch('/api/v1/chat', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            message: message,
                            session_id: this.sessionId,
                            user_id: 'web_user',
                            context: { interface: 'web', timestamp: new Date().toISOString() }
                        })
                    });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    const data = await response.json();
                    return {
                        response: data.response || data.message || 'I received your message.',
                        type: data.type || 'general',
                        confidence: data.confidence || 0.8
                    };
                } catch (error) {
                    console.error('API call failed:', error);
                    return {
                        response: "I'm A.T.L.A.S. powered by Predicto. I can help with stock analysis, trading strategies, and market insights. What would you like to know?",
                        type: 'fallback',
                        confidence: 0.7
                    };
                }
            }

            addMessage(content, sender, metadata = {}) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message ${sender}`;
                messageDiv.innerHTML = `<div class="message-content">${this.formatMessage(content)}</div>`;
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }

            formatMessage(content) {
                content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
                content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
                content = content.replace(/\n/g, '<br>');
                return content;
            }

            setTyping(isTyping) {
                this.isTyping = isTyping;
                this.sendButton.disabled = isTyping;
            }

            scrollToBottom() {
                requestAnimationFrame(() => {
                    this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
                });
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            new AtlasInterface();
        });
    </script>
</body>
</html>
