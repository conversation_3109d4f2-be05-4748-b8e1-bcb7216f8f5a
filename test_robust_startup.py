#!/usr/bin/env python3
"""
Test script for the robust server startup
"""

import sys
import os

# Add paths
sys.path.append('4_helper_tools')

try:
    print("Testing robust server startup...")
    
    # Test logging setup
    from atlas_logging_config import setup_atlas_logging, get_atlas_logger
    setup_atlas_logging('INFO')
    logger = get_atlas_logger('test')
    
    print("Logging setup successful")
    logger.info("Test log message - this should not have Unicode errors")
    
    # Test server manager import
    from atlas_robust_server_manager import RobustServerManager, check_system_requirements
    print("Server manager import successful")
    
    # Test system requirements
    if check_system_requirements():
        print("System requirements check passed")
    else:
        print("System requirements check failed")
    
    # Test port checking
    manager = RobustServerManager(None, port=8080)
    if manager.is_port_available(8080):
        print("Port 8080 is available")
    else:
        print("Port 8080 is not available")
        
    print("All tests completed successfully!")
    
except Exception as e:
    print(f"Error during testing: {e}")
    import traceback
    traceback.print_exc()
