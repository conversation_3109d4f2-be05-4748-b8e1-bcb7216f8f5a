#!/usr/bin/env python3
"""
Test script to verify Unicode fix is working
"""

import sys
import os

# Set environment variables for Windows Unicode support
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Add paths
sys.path.append('4_helper_tools')

print("Testing Unicode fix...")
print("=" * 50)

try:
    # Test the startup initialization
    from atlas_startup_init import ensure_windows_compatible_logging, get_atlas_logger
    
    print("✓ Successfully imported atlas_startup_init")
    
    # Initialize logging
    ensure_windows_compatible_logging()
    print("✓ Windows-compatible logging initialized")
    
    # Get a logger
    logger = get_atlas_logger('test_unicode')
    print("✓ Logger created successfully")
    
    # Test logging with emojis (should be converted to ASCII)
    logger.info("Testing emoji conversion: 🔮 ✅ 📊 🚀 ⚡")
    logger.info("Risk Engine test: ✅ Risk Engine initialization completed")
    logger.info("Education Engine test: 📚 Education Engine created")
    logger.info("Market Engine test: ✅ Market Engine initialization completed")
    logger.info("Trading Engine test: ✅ Trading Engine cleanup completed")
    logger.info("Predicto Engine test: 🔮 Predicto AI Engine cleanup completed")
    
    print("✓ All emoji logging tests completed without Unicode errors")
    print("✓ Unicode fix is working correctly!")
    
except ImportError as e:
    print(f"✗ Import error: {e}")
    print("Falling back to basic logging test...")
    
    # Basic logging test
    import logging
    logging.basicConfig(level=logging.INFO, format='%(asctime)s [%(levelname)s] %(name)s: %(message)s')
    logger = logging.getLogger('test_basic')
    
    try:
        logger.info("Testing basic logging without emojis")
        print("✓ Basic logging works")
    except Exception as e:
        print(f"✗ Basic logging failed: {e}")

except Exception as e:
    print(f"✗ Unexpected error: {e}")
    import traceback
    traceback.print_exc()

print("\nTest completed!")
