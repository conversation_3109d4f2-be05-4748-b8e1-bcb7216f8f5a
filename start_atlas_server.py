#!/usr/bin/env python3
"""
A.T.L.A.S. Enhanced Server Startup Script
Robust startup with port management and Windows compatibility
"""

import sys
import os

# Add all necessary paths
sys.path.append('4_helper_tools')
sys.path.append('1_main_chat_engine')
sys.path.append('2_trading_logic')
sys.path.append('3_market_news_data')

print("Starting A.T.L.A.S. Stock Market God Server...")
print("Enhanced startup with robust port management")
print("=" * 50)

try:
    # Import the robust server manager
    from atlas_robust_server_manager import start_atlas_server, check_system_requirements
    from atlas_logging_config import setup_atlas_logging, get_atlas_logger

    # Setup Windows-compatible logging
    setup_atlas_logging('INFO')
    logger = get_atlas_logger(__name__)

    logger.info("A.T.L.A.S. Enhanced Server Startup initiated")

    # Check system requirements
    if not check_system_requirements():
        logger.error("System requirements not met")
        sys.exit(1)

    # Import the FastAPI app
    from atlas_server import app

    logger.info("Starting A.T.L.A.S. with enhanced server manager")
    logger.info("Server will automatically find available port starting from 8080")

    # Start the server with robust management
    start_atlas_server(app, port=8080, host="0.0.0.0")

except ImportError as e:
    print(f"ERROR: Import failed: {e}")
    print("Please ensure all dependencies are installed")
    import traceback
    traceback.print_exc()
    sys.exit(1)
except Exception as e:
    print(f"ERROR: Server startup failed: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
