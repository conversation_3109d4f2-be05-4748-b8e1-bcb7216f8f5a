#!/usr/bin/env python3
"""
Direct Unicode Fix for A.T.L.A.S. Startup
This script applies the Windows-compatible logging fix directly
"""

import sys
import os
import logging
import re

# Set environment variables for Windows Unicode support
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Add paths
sys.path.append('4_helper_tools')
sys.path.append('1_main_chat_engine')
sys.path.append('2_trading_logic')
sys.path.append('3_market_news_data')

class WindowsUnicodeFormatter(logging.Formatter):
    """Direct Unicode-safe formatter for Windows"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Emoji replacements
        self.emoji_replacements = {
            '🔮': '[AI]',
            '✅': '[OK]',
            '❌': '[ERROR]',
            '⚠️': '[WARN]',
            '📊': '[DATA]',
            '🚀': '[LAUNCH]',
            '💼': '[TRADE]',
            '🎯': '[TARGET]',
            '🔍': '[SEARCH]',
            '📈': '[UP]',
            '📉': '[DOWN]',
            '💰': '[MONEY]',
            '🛡️': '[SHIELD]',
            '⭐': '[STAR]',
            '🔧': '[TOOL]',
            '🌐': '[WEB]',
            '📝': '[NOTE]',
            '🏁': '[FINISH]',
            '🎉': '[SUCCESS]',
            '🔗': '[LINK]',
            '🧠': '[BRAIN]',
            '📖': '[BOOK]',
            '📚': '[LIBRARY]',
            '📄': '[DOC]',
            '💡': '[IDEA]',
            '⚡': '[FAST]',
            '🧹': '[CLEAN]',
            '🎮': '[GAME]'
        }
    
    def format(self, record):
        # Get the original formatted message
        formatted = super().format(record)
        
        # Replace emojis
        for emoji, replacement in self.emoji_replacements.items():
            formatted = formatted.replace(emoji, replacement)
        
        # Remove any remaining Unicode characters that might cause issues
        try:
            formatted = formatted.encode('ascii', 'replace').decode('ascii')
        except UnicodeError:
            # Fallback: remove all non-ASCII characters
            formatted = ''.join(char for char in formatted if ord(char) < 128)
        
        return formatted

def setup_windows_safe_logging():
    """Setup Windows-safe logging for the entire system"""
    
    # Clear existing handlers
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create Windows-safe formatter
    formatter = WindowsUnicodeFormatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # File handler
    os.makedirs('logs', exist_ok=True)
    file_handler = logging.FileHandler('logs/atlas_unicode_safe.log', encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # Configure root logger
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Set specific loggers to appropriate levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    print("Windows-safe logging configured successfully")
    
    # Test the logging
    test_logger = logging.getLogger('unicode_fix_test')
    test_logger.info("Testing emoji conversion: 🔮 ✅ 📊 should become [AI] [OK] [DATA]")
    test_logger.info("Unicode fix applied successfully")

def main():
    """Main function to test and apply the Unicode fix"""
    
    print("A.T.L.A.S. Unicode Fix Utility")
    print("=" * 40)
    
    # Setup Windows-safe logging
    setup_windows_safe_logging()
    
    try:
        # Test importing A.T.L.A.S. server
        print("Testing A.T.L.A.S. server import...")
        from atlas_server import app
        print("✓ A.T.L.A.S. server imported successfully")
        
        # Start server with Unicode-safe logging
        print("Starting A.T.L.A.S. server with Unicode fix...")
        import uvicorn
        
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8080,
            reload=False,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure you're running from the correct directory")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
