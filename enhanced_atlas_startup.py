#!/usr/bin/env python3
"""
Enhanced A.T.L.A.S. Startup Script
Combines robust server management with comprehensive system checks and Windows compatibility
"""

import sys
import os
import time
from pathlib import Path

def setup_environment():
    """Setup the Python environment for A.T.L.A.S."""
    print("Setting up A.T.L.A.S. environment...")

    # Add all necessary paths
    paths_to_add = [
        '4_helper_tools',
        '1_main_chat_engine',
        '2_trading_logic',
        '3_market_news_data'
    ]

    for path in paths_to_add:
        if path not in sys.path:
            sys.path.append(path)

    # CRITICAL: Initialize Windows-compatible logging FIRST
    try:
        from atlas_startup_init import ensure_windows_compatible_logging, get_atlas_logger
        ensure_windows_compatible_logging()
        logger = get_atlas_logger('enhanced_startup')
        logger.info("Windows-compatible logging initialized in enhanced startup")
        print("Windows-compatible logging initialized successfully")
    except ImportError as e:
        print(f"Warning: Could not initialize enhanced logging: {e}")

    # Create necessary directories
    directories = ['logs', 'models', 'data', 'temp']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

    print("Environment setup completed")

def check_dependencies():
    """Check if all required dependencies are available."""
    print("Checking dependencies...")
    
    required_modules = [
        'fastapi',
        'uvicorn', 
        'psutil',
        'requests',
        'pandas',
        'numpy',
        'openai'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"ERROR: Missing required modules: {', '.join(missing_modules)}")
        print("Please install them using: pip install " + " ".join(missing_modules))
        return False
    
    print("All dependencies are available")
    return True

def display_startup_banner():
    """Display the A.T.L.A.S. startup banner."""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║        A.T.L.A.S. - Advanced Trading & Learning             ║
    ║                    Analysis System                           ║
    ║                                                              ║
    ║              Enhanced Startup Manager v2.0                  ║
    ║                                                              ║
    ║    Features:                                                 ║
    ║    • Robust port management                                  ║
    ║    • Windows compatibility                                   ║
    ║    • Automatic error recovery                                ║
    ║    • System requirements checking                            ║
    ║    • Enhanced logging                                        ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def main():
    """Main startup function."""
    display_startup_banner()
    
    # Setup environment
    setup_environment()
    
    # Check dependencies
    if not check_dependencies():
        print("\nStartup aborted due to missing dependencies.")
        sys.exit(1)
    
    try:
        # Import A.T.L.A.S. components
        print("Importing A.T.L.A.S. components...")
        
        from atlas_robust_server_manager import start_atlas_server, check_system_requirements
        from atlas_logging_config import setup_atlas_logging, get_atlas_logger
        
        # Setup Windows-compatible logging
        setup_atlas_logging('INFO')
        logger = get_atlas_logger('enhanced_startup')
        
        logger.info("Enhanced A.T.L.A.S. startup initiated")
        
        # Check system requirements
        print("Checking system requirements...")
        if not check_system_requirements():
            logger.error("System requirements not met")
            print("ERROR: System requirements not met. Please check the logs for details.")
            sys.exit(1)
        
        # Import the main server application
        print("Loading A.T.L.A.S. server application...")
        try:
            from atlas_server import app
            logger.info("A.T.L.A.S. server application loaded successfully")
        except ImportError as e:
            logger.error(f"Failed to import atlas_server: {e}")
            print("ERROR: Could not load A.T.L.A.S. server application")
            print("Please ensure you're running from the correct directory")
            sys.exit(1)
        
        # Display startup information
        print("\n" + "="*60)
        print("A.T.L.A.S. ENHANCED STARTUP")
        print("="*60)
        print("• Robust port management: ENABLED")
        print("• Windows compatibility: ENABLED") 
        print("• Automatic error recovery: ENABLED")
        print("• Enhanced logging: ENABLED")
        print("• Starting port: 8080 (will auto-find if busy)")
        print("• Host: 0.0.0.0 (all interfaces)")
        print("="*60)
        
        logger.info("Starting A.T.L.A.S. with enhanced server manager")
        
        # Start the server with robust management
        start_atlas_server(app, port=8080, host="0.0.0.0")
        
    except ImportError as e:
        print(f"\nERROR: Failed to import required modules: {e}")
        print("This might be due to:")
        print("1. Missing dependencies (run: pip install -r requirements.txt)")
        print("2. Incorrect working directory")
        print("3. Python path issues")
        
        # Fallback startup
        print("\nAttempting fallback startup...")
        try:
            import uvicorn
            sys.path.append('1_main_chat_engine')
            from atlas_server import app
            
            print("Starting with basic uvicorn (no enhanced features)...")
            uvicorn.run(
                app,
                host="0.0.0.0",
                port=8080,
                reload=False,
                log_level="info"
            )
        except Exception as fallback_error:
            print(f"Fallback startup also failed: {fallback_error}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        sys.exit(0)
        
    except Exception as e:
        print(f"\nERROR: Unexpected error during startup: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
