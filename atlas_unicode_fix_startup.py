#!/usr/bin/env python3
"""
A.T.L.A.S. Unicode Fix Startup Script
Direct solution to eliminate Windows Unicode encoding errors
"""

import sys
import os
import logging
import re

# Set environment variables for Windows Unicode support
os.environ['PYTHONIOENCODING'] = 'utf-8'

# Add paths
sys.path.append('4_helper_tools')
sys.path.append('1_main_chat_engine')
sys.path.append('2_trading_logic')
sys.path.append('3_market_news_data')

class WindowsUnicodeFormatter(logging.Formatter):
    """Direct Unicode-safe formatter for Windows"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Comprehensive emoji replacements based on actual A.T.L.A.S. usage
        self.emoji_replacements = {
            '✅': '[OK]',           # \u2705
            '📚': '[LIBRARY]',      # \U0001f4da
            '📖': '[BOOK]',         # \U0001f4d6
            '🧹': '[CLEAN]',        # \U0001f9f9
            '🔮': '[AI]',           # \U0001f52e
            '❌': '[ERROR]',        # \u274c
            '⚠️': '[WARN]',         # \u26a0
            '📊': '[DATA]',         # \U0001f4ca
            '🚀': '[LAUNCH]',       # \U0001f680
            '💼': '[TRADE]',        # \U0001f4bc
            '🎯': '[TARGET]',       # \U0001f3af
            '🔍': '[SEARCH]',       # \U0001f50d
            '📈': '[UP]',           # \U0001f4c8
            '📉': '[DOWN]',         # \U0001f4c9
            '💰': '[MONEY]',        # \U0001f4b0
            '🛡️': '[SHIELD]',       # \U0001f6e1
            '⭐': '[STAR]',         # \u2b50
            '🔧': '[TOOL]',         # \U0001f527
            '🌐': '[WEB]',          # \U0001f310
            '📝': '[NOTE]',         # \U0001f4dd
            '🏁': '[FINISH]',       # \U0001f3c1
            '🎉': '[SUCCESS]',      # \U0001f389
            '🔗': '[LINK]',         # \U0001f517
            '🧠': '[BRAIN]',        # \U0001f9e0
            '📄': '[DOC]',          # \U0001f4c4
            '💡': '[IDEA]',         # \U0001f4a1
            '⚡': '[FAST]',         # \u26a1
            '🎮': '[GAME]',         # \U0001f3ae
        }
    
    def format(self, record):
        # Get the original formatted message
        formatted = super().format(record)
        
        # Replace emojis with ASCII equivalents
        for emoji, replacement in self.emoji_replacements.items():
            formatted = formatted.replace(emoji, replacement)
        
        # Remove any remaining Unicode characters that might cause issues
        try:
            # Try to encode as ASCII, replacing problematic characters
            formatted = formatted.encode('ascii', 'replace').decode('ascii')
        except UnicodeError:
            # Fallback: remove all non-ASCII characters
            formatted = ''.join(char for char in formatted if ord(char) < 128)
        
        return formatted

def setup_windows_safe_logging():
    """Setup Windows-safe logging for the entire system"""
    
    print("Setting up Windows-safe logging...")
    
    # Clear existing handlers from root logger
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create Windows-safe formatter
    formatter = WindowsUnicodeFormatter(
        '%(asctime)s [%(levelname)s] %(name)s: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler with Windows-safe encoding
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # File handler
    os.makedirs('logs', exist_ok=True)
    file_handler = logging.FileHandler('logs/atlas_unicode_safe.log', encoding='utf-8')
    file_handler.setFormatter(formatter)
    file_handler.setLevel(logging.DEBUG)
    
    # Configure root logger
    root_logger.setLevel(logging.INFO)
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # Set specific loggers to appropriate levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    
    print("✓ Windows-safe logging configured successfully")
    
    # Test the logging with emojis
    test_logger = logging.getLogger('unicode_fix_test')
    test_logger.info("Testing emoji conversion: ✅ 📚 📖 🧹 🔮 should become [OK] [LIBRARY] [BOOK] [CLEAN] [AI]")
    test_logger.info("Unicode fix applied successfully - no more encoding errors!")

def main():
    """Main function to test and apply the Unicode fix"""
    
    print("A.T.L.A.S. Unicode Fix Startup")
    print("=" * 50)
    
    # Setup Windows-safe logging FIRST
    setup_windows_safe_logging()
    
    try:
        # Test importing A.T.L.A.S. server
        print("Testing A.T.L.A.S. server import...")
        from atlas_server import app
        print("✓ A.T.L.A.S. server imported successfully")
        
        # Start server with Unicode-safe logging
        print("Starting A.T.L.A.S. server with Unicode fix...")
        print("Server will be available at: http://localhost:8080")
        print("API docs: http://localhost:8080/docs")
        print("Health check: http://localhost:8080/api/v1/health")
        print("=" * 50)
        
        import uvicorn
        
        # Start server with Windows-safe configuration
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8080,
            reload=False,
            log_level="info"
        )
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Please ensure you're running from the correct directory")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
